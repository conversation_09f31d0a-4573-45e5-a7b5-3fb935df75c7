# MiniAppInitializer

재사용 가능한 Telegram Mini App 초기화 컴포넌트입니다. 다른 미니앱 프로젝트에서도 쉽게 사용할 수 있도록 설계되었습니다.

## 특징

- 🚀 **자동 SDK 초기화**: Telegram Mini App SDK 자동 설정
- 🧪 **개발 환경 지원**: 개발시 텔레그램 환경 모킹
- 🎨 **테마 통합**: 자동 테마 감지 및 CSS 변수 바인딩
- 🌍 **다국어 지원**: 사용자 로케일 자동 감지 및 설정
- 💰 **TON Connect**: TON 블록체인 지갑 연결 기능
- 🛡️ **에러 처리**: 내장된 에러 경계 및 재시도 기능
- 📱 **플랫폼 감지**: iOS, Android, macOS, Desktop 자동 감지
- 🐛 **디버깅 도구**: Eruda 등 모바일 디버깅 도구 지원

## 기본 사용법

```tsx
import { MiniAppInitializer } from '@/components/MiniAppInitializer';

function App() {
  return (
    <MiniAppInitializer>
      <YourAppContent />
    </MiniAppInitializer>
  );
}
```

## 고급 사용법

```tsx
import { MiniAppInitializer } from '@/components/MiniAppInitializer';

// 커스텀 로딩 컴포넌트
const CustomLoader = () => (
  <div className="loading-spinner">
    <div className="spinner" />
    <p>앱을 초기화하는 중...</p>
  </div>
);

// 커스텀 에러 컴포넌트
const CustomErrorFallback = ({ error, resetError }) => (
  <div className="error-container">
    <h2>초기화 실패</h2>
    <p>{error.message}</p>
    <button onClick={resetError}>다시 시도</button>
  </div>
);

function App() {
  return (
    <MiniAppInitializer
      config={{
        debug: process.env.NODE_ENV === 'development',
        eruda: true, // 모바일에서 디버깅 툴 활성화
        autoSetLocale: true, // 자동 로케일 설정
      }}
      mockConfig={{
        themeParams: {
          bg_color: '#ffffff',
          text_color: '#000000',
          // ... 커스텀 테마
        },
        skipMocking: false, // 개발시에도 모킹 사용
      }}
      LoadingComponent={CustomLoader}
      ErrorFallback={CustomErrorFallback}
      tonConnectManifestUrl="/my-tonconnect-manifest.json"
      onInitComplete={() => console.log('초기화 완료!')}
      onInitError={(error) => console.error('초기화 실패:', error)}
      className="mini-app-root"
    >
      <YourAppContent />
    </MiniAppInitializer>
  );
}
```

## 설정 옵션

### MiniAppInitConfig

| 속성 | 타입 | 기본값 | 설명 |
|------|------|--------|------|
| `debug` | `boolean` | `auto` | 디버그 모드 활성화 |
| `eruda` | `boolean` | `false` | 모바일 디버깅 툴 활성화 |
| `mockForMacOS` | `boolean` | `false` | macOS 특화 워크어라운드 |
| `autoSetLocale` | `boolean` | `true` | 자동 로케일 감지 및 설정 |
| `mockThemeParams` | `ThemeParams` | - | 개발용 커스텀 테마 |

### MockEnvConfig

| 속성 | 타입 | 기본값 | 설명 |
|------|------|--------|------|
| `themeParams` | `ThemeParams` | 기본 다크 테마 | 개발용 테마 파라미터 |
| `launchParams` | `URLSearchParams` | 기본 파라미터 | 개발용 런치 파라미터 |
| `skipMocking` | `boolean` | `false` | 환경 모킹 건너뛰기 |

## 훅 사용법

### useMiniAppInit

```tsx
import { useMiniAppInit } from '@/components/MiniAppInitializer';

function CustomInitializer() {
  const { isInitialized, isInitializing, error, retry } = useMiniAppInit({
    debug: true,
    autoSetLocale: true,
  });

  if (isInitializing) return <div>로딩중...</div>;
  if (error) return <div>에러: {error.message} <button onClick={retry}>재시도</button></div>;
  if (!isInitialized) return <div>초기화 대기중...</div>;

  return <YourApp />;
}
```

### useAutoLocale

```tsx
import { useAutoLocale } from '@/components/MiniAppInitializer';

function YourComponent() {
  // 텔레그램 사용자의 언어 설정을 자동으로 적용
  useAutoLocale(true);
  
  return <div>다국어 지원 컴포넌트</div>;
}
```

## 유틸리티 함수

```tsx
import {
  detectPlatform,
  shouldEnableDebug,
  setupMockEnvironment,
  initializeMiniApp,
  getPlatformConfig,
} from '@/components/MiniAppInitializer';

// 플랫폼 감지
const platform = detectPlatform(); // 'ios' | 'android' | 'macos' | 'tdesktop' | 'web' | 'unknown'

// 디버그 모드 확인
const debug = shouldEnableDebug();

// 수동 초기화
await setupMockEnvironment();
await initializeMiniApp({ debug: true });

// 플랫폼별 설정
const { platform, appearance } = getPlatformConfig();
```

## 마이그레이션 가이드

기존 프로젝트에서 MiniAppInitializer로 마이그레이션하는 방법:

### Before (기존 방식)

```tsx
// layout.tsx
import { Root } from '@/components/Root/Root';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <I18nProvider>
          <Root>{children}</Root>
        </I18nProvider>
      </body>
    </html>
  );
}
```

### After (새로운 방식)

```tsx
// layout.tsx
import { MiniAppInitializer } from '@/components/MiniAppInitializer';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <I18nProvider>
          <MiniAppInitializer>
            {children}
          </MiniAppInitializer>
        </I18nProvider>
      </body>
    </html>
  );
}
```

## 다른 프로젝트에서 사용하기

1. **컴포넌트 복사**: `MiniAppInitializer` 폴더를 새 프로젝트로 복사
2. **의존성 설치**: 필요한 패키지들 설치

   ```bash
   npm install @telegram-apps/sdk-react @telegram-apps/telegram-ui @tonconnect/ui-react
   ```

3. **설정 수정**: 프로젝트에 맞게 import 경로 및 설정 조정
4. **적용**: 기존 Root 컴포넌트를 MiniAppInitializer로 교체

## 트러블슈팅

### 일반적인 문제들

1. **"Module not found" 에러**
   - import 경로를 프로젝트 구조에 맞게 수정하세요
   - `@/` alias가 설정되어 있는지 확인하세요

2. **초기화 실패**
   - 브라우저 콘솔에서 에러 메시지 확인
   - `config.debug = true`로 설정하여 상세 로그 확인

3. **테마가 적용되지 않음**
   - CSS 변수가 올바르게 바인딩되었는지 확인
   - `bindThemeParamsCssVars()` 호출 여부 확인

4. **모바일에서 디버깅**
   - `config.eruda = true`로 설정
   - 물리 디바이스에서 테스트

## 라이센스

이 컴포넌트는 원본 프로젝트와 동일한 라이센스를 따릅니다.
