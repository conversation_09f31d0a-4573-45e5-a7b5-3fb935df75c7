'use client';

import React, { useState } from 'react';
import { initData, miniApp, useLaunchParams, useSignal } from '@telegram-apps/sdk-react';

interface MiniAppIndicatorProps {
  isInitialized: boolean;
  shouldInitMiniApp: boolean;
  className?: string;
}

/**
 * MiniApp 초기화 상태 인디케이터
 */
export const MiniAppIndicator: React.FC<MiniAppIndicatorProps> = ({
  isInitialized,
  shouldInitMiniApp,
  className = '',
}) => {
  if (!shouldInitMiniApp) return null;

  return (
    <div className={`fixed top-4 right-4 z-50 ${className}`}>
      <div
        className={`flex items-center gap-2 rounded-lg px-3 py-2 shadow-lg backdrop-blur-sm ${
          isInitialized ? 'bg-green-500/90 text-white' : 'animate-pulse bg-orange-500/90 text-white'
        } `}
      >
        <div
          className={`h-2 w-2 rounded-full ${isInitialized ? 'bg-white' : 'animate-ping bg-white'} `}
        />
        <span className="text-sm font-medium">
          {isInitialized ? 'MiniApp Ready' : 'Initializing...'}
        </span>
      </div>
    </div>
  );
};
