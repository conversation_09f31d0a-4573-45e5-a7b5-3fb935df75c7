import {
  setDebug,
  mountBackButton,
  restoreInitData,
  init as initSDK,
  mountMiniAppSync,
  bindThemeParamsCssVars,
  mountViewport,
  bindViewportCssVars,
  mockTelegramEnv,
  type ThemeParams,
  themeParamsState,
  retrieveLaunchParams,
  emitEvent,
  isTMA,
} from '@telegram-apps/sdk-react';

import type { MiniAppInitConfig, MockEnvConfig, Platform } from './types';

/**
 * Default theme parameters for development
 */
export const DEFAULT_THEME_PARAMS: ThemeParams = {
  accent_text_color: '#6ab2f2',
  bg_color: '#17212b',
  button_color: '#5288c1',
  button_text_color: '#ffffff',
  destructive_text_color: '#ec3942',
  header_bg_color: '#17212b',
  hint_color: '#708499',
  link_color: '#6ab3f3',
  secondary_bg_color: '#232e3c',
  section_bg_color: '#17212b',
  section_header_text_color: '#6ab3f3',
  subtitle_text_color: '#708499',
  text_color: '#f5f5f5',
} as const;

/**
 * Detect platform from launch parameters
 */
export function detectPlatform(): Platform {
  try {
    const launchParams = retrieveLaunchParams();
    const platform = launchParams.tgWebAppPlatform;

    if (['ios', 'android', 'macos', 'tdesktop'].includes(platform)) {
      return platform as Platform;
    }

    return 'web';
  } catch {
    return 'unknown';
  }
}

/**
 * Check if debug mode should be enabled
 */
export function shouldEnableDebug(): boolean {
  try {
    const launchParams = retrieveLaunchParams();
    return (
      (launchParams.tgWebAppStartParam || '').includes('debug') ||
      process.env.NODE_ENV === 'development'
    );
  } catch {
    return process.env.NODE_ENV === 'development';
  }
}

/**
 * Setup mock environment for development
 */
export async function setupMockEnvironment(config?: MockEnvConfig): Promise<void> {
  if (process.env.NODE_ENV !== 'development' || config?.skipMocking) {
    return;
  }

  const isTma = await isTMA('complete');
  if (isTma) {
    return; // Already in Telegram environment
  }

  const themeParams = config?.themeParams || DEFAULT_THEME_PARAMS;
  const noInsets = { left: 0, top: 0, bottom: 0, right: 0 } as const;

  mockTelegramEnv({
    onEvent(e) {
      if (e[0] === 'web_app_request_theme') {
        return emitEvent('theme_changed', { theme_params: themeParams });
      }
      if (e[0] === 'web_app_request_viewport') {
        return emitEvent('viewport_changed', {
          height: window.innerHeight,
          width: window.innerWidth,
          is_expanded: true,
          is_state_stable: true,
        });
      }
      if (e[0] === 'web_app_request_content_safe_area') {
        return emitEvent('content_safe_area_changed', noInsets);
      }
      if (e[0] === 'web_app_request_safe_area') {
        return emitEvent('safe_area_changed', noInsets);
      }
    },
    launchParams:
      config?.launchParams ||
      new URLSearchParams([
        ['tgWebAppThemeParams', JSON.stringify(themeParams)],
        [
          'tgWebAppData',
          new URLSearchParams([
            ['auth_date', ((new Date().getTime() / 1000) | 0).toString()],
            ['hash', 'development-hash'],
            ['signature', 'development-signature'],
            ['user', JSON.stringify({ id: 1, first_name: 'Developer' })],
          ]).toString(),
        ],
        ['tgWebAppVersion', '8.4'],
        ['tgWebAppPlatform', 'tdesktop'],
      ]),
  });

  console.info(
    '⚠️ Development mode: Telegram environment mocked. This behavior is only active in development.'
  );
}

/**
 * Initialize Telegram Mini App SDK
 */
export async function initializeMiniApp(config: MiniAppInitConfig = {}): Promise<void> {
  const { debug = shouldEnableDebug(), eruda = false, mockForMacOS = false } = config;

  // Set debug mode and initialize SDK
  setDebug(debug);
  initSDK();

  // Add Eruda for mobile debugging
  if (eruda && debug) {
    const platform = detectPlatform();
    if (['ios', 'android'].includes(platform)) {
      try {
        const { default: eruda } = await import('eruda');
        eruda.init();
        eruda.position({ x: window.innerWidth - 50, y: 0 });
      } catch (error) {
        console.warn('Failed to load Eruda:', error);
      }
    }
  }

  // macOS-specific workarounds
  if (mockForMacOS && detectPlatform() === 'macos') {
    let firstThemeSent = false;
    mockTelegramEnv({
      onEvent(event, next) {
        if (event[0] === 'web_app_request_theme') {
          let tp: ThemeParams = {};
          if (firstThemeSent) {
            tp = themeParamsState();
          } else {
            firstThemeSent = true;
            tp ||= retrieveLaunchParams().tgWebAppThemeParams;
          }
          return emitEvent('theme_changed', { theme_params: tp });
        }

        if (event[0] === 'web_app_request_safe_area') {
          return emitEvent('safe_area_changed', {
            left: 0,
            top: 0,
            right: 0,
            bottom: 0,
          });
        }

        next();
      },
    });
  }

  // Mount Telegram Mini App components
  mountBackButton.ifAvailable();
  restoreInitData();

  if (mountMiniAppSync.isAvailable()) {
    mountMiniAppSync();
    bindThemeParamsCssVars();
  }

  if (mountViewport.isAvailable()) {
    try {
      await mountViewport();
      bindViewportCssVars();
    } catch (error) {
      console.warn('Failed to mount viewport:', error);
    }
  }
}

/**
 * Get platform-specific configuration
 */
export function getPlatformConfig(): {
  platform: Platform;
  appearance: 'ios' | 'base';
} {
  const platform = detectPlatform();
  const appearance = ['macos', 'ios'].includes(platform) ? 'ios' : 'base';

  return { platform, appearance };
}
