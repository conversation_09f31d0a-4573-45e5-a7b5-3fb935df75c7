import { useState, useEffect, useCallback } from 'react';
import type { MiniAppInitConfig, MockEnvConfig, MiniAppInitState } from './types';
import { setupMockEnvironment, initializeMiniApp } from './utils';
import { useDidMount } from '@/hooks/useDidMount';
import { initData } from '@telegram-apps/sdk-react';

/**
 * Custom hook for managing Mini App initialization state
 */
export function useMiniAppInit(
  config?: MiniAppInitConfig,
  mockConfig?: MockEnvConfig
): MiniAppInitState {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const didMount = useDidMount();

  const initialize = useCallback(async () => {
    if (isInitializing || isInitialized) return;

    // If config is undefined, skip initialization (miniapp=true not present)
    if (config === undefined) {
      setIsInitialized(true);
      return;
    }

    setIsInitializing(true);
    setError(null);

    try {
      // Setup mock environment first
      await setupMockEnvironment(mockConfig);

      // Initialize Mini App SDK
      await initializeMiniApp(config);

      setIsInitialized(true);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Initialization failed');
      setError(error);
      console.error('Mini App initialization failed:', error);
    } finally {
      setIsInitializing(false);
    }
  }, [config, mockConfig, isInitializing, isInitialized]);

  const retry = useCallback(() => {
    setIsInitialized(false);
    setError(null);
    initialize();
  }, [initialize]);

  const resetError = useCallback(() => {
    setError(null);
  }, []);

  // Initialize when component mounts (client-side only)
  useEffect(() => {
    if (didMount && !isInitialized && !isInitializing && !error) {
      initialize();
    }
  }, [didMount, isInitialized, isInitializing, error, initialize]);

  return {
    isInitialized,
    isInitializing,
    error,
    retry,
    resetError,
  };
}

/**
 * Hook for automatic locale setting based on Telegram user data
 */
export function useAutoLocale(enabled: boolean = true) {
  useEffect(() => {
    if (!enabled) return;

    const setUserLocale = async () => {
      try {
        const user = initData.user();
        console.log('user', user);
      } catch (error) {
        console.warn('Failed to set user locale:', error);
      }
    };

    setUserLocale();
  }, [enabled]);
}
