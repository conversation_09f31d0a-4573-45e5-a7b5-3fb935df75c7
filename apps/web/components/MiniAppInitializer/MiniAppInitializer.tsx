'use client';

import { initData, miniApp, useLaunchParams, useSignal } from '@telegram-apps/sdk-react';
import { AppRoot } from '@telegram-apps/telegram-ui';
import React, { useEffect } from 'react';
// import { ErrorBoundary } from '@/components/ErrorBoundary';
// import { ErrorPage } from '@/components/ErrorPage';
import { useMiniAppInit } from './hooks';
import type { MiniAppInitializerProps } from './types';
import { getPlatformConfig } from './utils';
import { MiniAppIndicator } from './MiniAppIndicator';
import { MiniAppDebugPanel } from './MiniAppDebugPanel';

/**
 * Default loading component
 */
const DefaultLoader: React.FC = () => (
  <div
    style={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100vh',
      fontSize: '16px',
      color: 'var(--tg-theme-text-color, #000)',
      background: 'var(--tg-theme-bg-color, #fff)',
    }}
  >
    Loading Mini App...
  </div>
);

/**
 * Default error fallback component
 */
const DefaultErrorFallback: React.FC<{
  error: Error;
  resetError: () => void;
}> = ({ error, resetError }) => (
  <div
    style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100vh',
      padding: '20px',
      textAlign: 'center',
      color: 'var(--tg-theme-text-color, #000)',
      background: 'var(--tg-theme-bg-color, #fff)',
    }}
  >
    <h2 style={{ marginBottom: '16px' }}>Initialization Failed</h2>
    <p style={{ marginBottom: '20px', opacity: 0.7 }}>
      {error.message || 'Something went wrong during Mini App initialization'}
    </p>
    <button
      onClick={resetError}
      style={{
        padding: '12px 24px',
        background: 'var(--tg-theme-button-color, #007AFF)',
        color: 'var(--tg-theme-button-text-color, #fff)',
        border: 'none',
        borderRadius: '8px',
        cursor: 'pointer',
        fontSize: '16px',
      }}
    >
      Retry
    </button>
  </div>
);

/**
 * Inner component that renders after initialization
 */
const MiniAppContent: React.FC<MiniAppInitializerProps> = ({ children, className, style }) => {
  const lp = useLaunchParams();
  const isDark = useSignal(miniApp.isDark);
  const initDataUser = useSignal(initData.user);
  const { appearance } = getPlatformConfig();

  return (
    <div className={className} style={style}>
      {/* <TonConnectUIProvider manifestUrl={tonConnectManifestUrl}> */}
      <AppRoot appearance={isDark ? 'dark' : 'light'} platform={appearance}>
        {children}
      </AppRoot>
      {/* </TonConnectUIProvider> */}
    </div>
  );
};

/**
 * Main MiniAppInitializer component
 *
 * This component handles all the initialization logic for Telegram Mini Apps
 * and provides a clean, reusable interface for other projects.
 *
 * Features:
 * - Automatic SDK initialization
 * - Development environment mocking
 * - Error boundary with retry functionality
 * - Loading states
 * - Platform-specific configurations
 * - TON Connect integration
 * - Automatic locale setting
 *
 * @example
 * ```tsx
 * <MiniAppInitializer
 *   config={{
 *     debug: true,
 *     autoSetLocale: true,
 *   }}
 *   mockConfig={{
 *     themeParams: customTheme,
 *   }}
 * >
 *   <YourApp />
 * </MiniAppInitializer>
 * ```
 */
export const MiniAppInitializer: React.FC<MiniAppInitializerProps> = ({
  children,
  config,
  mockConfig,
  LoadingComponent = DefaultLoader,
  ErrorFallback = DefaultErrorFallback,
  onInitComplete,
  onInitError,
  ...props
}) => {
  // Check if miniapp=true parameter exists in URL
  const shouldInitMiniApp = React.useMemo(() => {
    if (typeof window === 'undefined') return false;
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('miniapp') === 'true';
  }, []);

  const { isInitialized, isInitializing, error, retry, resetError } = useMiniAppInit(
    shouldInitMiniApp ? config : undefined,
    shouldInitMiniApp ? mockConfig : undefined
  );

  // Call lifecycle callbacks
  useEffect(() => {
    if (isInitialized && onInitComplete) {
      onInitComplete();
    }
  }, [isInitialized, onInitComplete]);

  useEffect(() => {
    if (error && onInitError) {
      onInitError(error);
    }
  }, [error, onInitError]);

  // If miniapp parameter is not true, just render children without initialization
  if (!shouldInitMiniApp) {
    return <>{children}</>;
  }

  if (isInitializing || !isInitialized) {
    if (error) {
      return (
        <>
          <ErrorFallback
            error={error}
            resetError={() => {
              resetError();
              retry();
            }}
          />
          {/* Show debug panel even in error state */}
          <MiniAppDebugPanel
            isInitialized={false}
            shouldInitMiniApp={shouldInitMiniApp}
            error={error}
          />
        </>
      );
    }
    return (
      <>
        <LoadingComponent />
        <MiniAppIndicator isInitialized={false} shouldInitMiniApp={shouldInitMiniApp} />
        <MiniAppDebugPanel
          isInitialized={false}
          shouldInitMiniApp={shouldInitMiniApp}
          error={error}
        />
      </>
    );
  }

  // Render the app with error boundary
  return (
    <>
      <MiniAppContent {...props} config={config}>
        {children}
      </MiniAppContent>
      <MiniAppIndicator isInitialized={isInitialized} shouldInitMiniApp={shouldInitMiniApp} />
      <MiniAppDebugPanel
        isInitialized={isInitialized}
        shouldInitMiniApp={shouldInitMiniApp}
        error={error}
      />
    </>
  );
};
