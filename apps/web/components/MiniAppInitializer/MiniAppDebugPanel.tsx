'use client';

import React, { useState } from 'react';
import { initData, miniApp, useLaunchParams, useSignal } from '@telegram-apps/sdk-react';
import Image from 'next/image';

interface MiniAppDebugPanelProps {
  isInitialized: boolean;
  shouldInitMiniApp: boolean;
  error?: Error | null;
  className?: string;
}

/**
 * MiniApp 디버그 패널 - 개발 시 MiniApp 상태와 데이터를 확인할 수 있는 UI
 */
export const MiniAppDebugPanel: React.FC<MiniAppDebugPanelProps> = ({
  isInitialized,
  shouldInitMiniApp,
  error,
  className = '',
}) => {
  const [isOpen, setIsOpen] = useState(false);

  // MiniApp 관련 상태들
  const lp = useLaunchParams();
  const isDark = useSignal(miniApp.isDark);
  const initDataUser = useSignal(initData.user);
  const initDataRaw = useSignal(initData.raw);

  if (!shouldInitMiniApp) return null;

  const debugData = {
    status: {
      isInitialized,
      shouldInitMiniApp,
      hasError: !!error,
      errorMessage: error?.message,
    },
    theme: {
      isDark,
      appearance: isDark ? 'dark' : 'light',
    },
    launchParams: lp
      ? {
          platform: lp.platform,
          version: lp.version,
          showSettings: lp.showSettings,
          botInline: lp.botInline,
        }
      : null,
    initData: {
      user: initDataUser
        ? {
            id: initDataUser.id,
            firstName: initDataUser.first_name,
            lastName: initDataUser.last_name,
            username: initDataUser.username,
            languageCode: initDataUser.language_code,
            isPremium: initDataUser.is_premium,
          }
        : null,
      authDate: initData.authDate(),
      hash: initData.hash(),
      queryId: initData.queryId(),
      rawData: initDataRaw,
    },
    environment: {
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'N/A',
      url: typeof window !== 'undefined' ? window.location.href : 'N/A',
      timestamp: new Date().toISOString(),
    },
  };

  return (
    <>
      {/* Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`fixed right-4 bottom-4 z-50 flex h-12 w-12 items-center justify-center rounded-full shadow-lg backdrop-blur-sm ${
          isInitialized
            ? 'bg-blue-500/90 hover:bg-blue-600/90'
            : 'bg-gray-500/90 hover:bg-gray-600/90'
        } text-white transition-all duration-200 hover:scale-110 ${className} `}
        title="Toggle MiniApp Debug Panel"
      >
        <Image src="/assets/icons/sm-icons/settings.svg" alt="Debug" width={20} height={20} />
      </button>

      {/* Debug Panel */}
      {isOpen && (
        <div className="fixed inset-0 z-40 flex items-center justify-center bg-black/50 p-4 backdrop-blur-sm">
          <div className="max-h-[80vh] w-full max-w-2xl overflow-hidden rounded-lg bg-white shadow-xl dark:bg-gray-900">
            {/* Header */}
            <div className="flex items-center justify-between border-b border-gray-200 p-4 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                MiniApp Debug Panel
              </h2>
              <button
                onClick={() => setIsOpen(false)}
                className="rounded-md p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:hover:bg-gray-800 dark:hover:text-gray-300"
              >
                <Image src="/assets/icons/xs-icons/delete.svg" alt="Close" width={20} height={20} />
              </button>
            </div>

            {/* Content */}
            <div className="max-h-[calc(80vh-4rem)] overflow-y-auto p-4">
              <div className="space-y-6">
                {/* Status Section */}
                <section>
                  <h3 className="text-md mb-2 font-medium text-gray-900 dark:text-white">
                    초기화 상태
                  </h3>
                  <div className="space-y-2 rounded-lg bg-gray-50 p-3 dark:bg-gray-800">
                    <div className="flex items-center gap-2">
                      <div
                        className={`h-2 w-2 rounded-full ${isInitialized ? 'bg-green-500' : 'bg-orange-500'}`}
                      />
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        상태: {isInitialized ? '초기화 완료' : '초기화 중...'}
                      </span>
                    </div>
                    {error && (
                      <div className="text-sm text-red-600 dark:text-red-400">
                        에러: {error.message}
                      </div>
                    )}
                  </div>
                </section>

                {/* Theme Section */}
                <section>
                  <h3 className="text-md mb-2 font-medium text-gray-900 dark:text-white">
                    테마 정보
                  </h3>
                  <pre className="overflow-x-auto rounded-lg bg-gray-50 p-3 text-sm text-gray-700 dark:bg-gray-800 dark:text-gray-300">
                    {JSON.stringify(debugData.theme, null, 2)}
                  </pre>
                </section>

                {/* Launch Params Section */}
                {debugData.launchParams && (
                  <section>
                    <h3 className="text-md mb-2 font-medium text-gray-900 dark:text-white">
                      실행 파라미터
                    </h3>
                    <pre className="overflow-x-auto rounded-lg bg-gray-50 p-3 text-sm text-gray-700 dark:bg-gray-800 dark:text-gray-300">
                      {JSON.stringify(debugData.launchParams, null, 2)}
                    </pre>
                  </section>
                )}

                {/* Init Data Section */}
                <section>
                  <h3 className="text-md mb-2 font-medium text-gray-900 dark:text-white">
                    초기화 데이터
                  </h3>
                  <pre className="overflow-x-auto rounded-lg bg-gray-50 p-3 text-sm text-gray-700 dark:bg-gray-800 dark:text-gray-300">
                    {JSON.stringify(debugData.initData, null, 2)}
                  </pre>
                </section>

                {/* Environment Section */}
                <section>
                  <h3 className="text-md mb-2 font-medium text-gray-900 dark:text-white">
                    환경 정보
                  </h3>
                  <pre className="overflow-x-auto rounded-lg bg-gray-50 p-3 text-sm text-gray-700 dark:bg-gray-800 dark:text-gray-300">
                    {JSON.stringify(debugData.environment, null, 2)}
                  </pre>
                </section>
              </div>
            </div>

            {/* Footer */}
            <div className="border-t border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-800">
              <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                <span>개발용 디버그 패널</span>
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(JSON.stringify(debugData, null, 2));
                    // TODO: Toast notification for copy success
                  }}
                  className="rounded bg-blue-500 px-3 py-1 text-sm text-white transition-colors hover:bg-blue-600"
                >
                  JSON 복사
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
