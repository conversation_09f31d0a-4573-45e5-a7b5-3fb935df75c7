import { type PropsWithChildren } from "react";
import { type ThemeParams } from "@telegram-apps/sdk-react";

/**
 * Configuration options for Telegram Mini App initialization
 */
export interface MiniAppInitConfig {
  /** Enable debug mode */
  debug?: boolean;
  /** Enable Eruda debugging tool for mobile platforms */
  eruda?: boolean;
  /** Enable macOS-specific workarounds */
  mockForMacOS?: boolean;
  /** Custom theme parameters for development */
  mockThemeParams?: ThemeParams;
  /** Enable automatic locale detection and setting */
  autoSetLocale?: boolean;
  /** Custom init data for development */
  mockInitData?: Record<string, any>;
}

/**
 * Environment mock configuration
 */
export interface MockEnvConfig {
  /** Custom theme parameters */
  themeParams?: ThemeParams;
  /** Custom launch parameters */
  launchParams?: URLSearchParams;
  /** Skip environment mocking */
  skipMocking?: boolean;
}

/**
 * Props for MiniAppInitializer component
 */
export interface MiniAppInitializerProps extends PropsWithChildren {
  /** Initialization configuration */
  config?: MiniAppInitConfig;
  /** Mock environment configuration for development */
  mockConfig?: MockEnvConfig;
  /** Loading component to show during initialization */
  LoadingComponent?: React.ComponentType;
  /** Error boundary fallback component */
  ErrorFallback?: React.ComponentType<{ error: Error; resetError: () => void }>;
  /** Callback fired when initialization is complete */
  onInitComplete?: () => void;
  /** Callback fired when initialization fails */
  onInitError?: (error: Error) => void;
  /** TON Connect manifest URL */
  tonConnectManifestUrl?: string;
  /** Custom CSS class for the root container */
  className?: string;
  /** Custom inline styles for the root container */
  style?: React.CSSProperties;
}

/**
 * Hook return type for useMiniAppInit
 */
export interface MiniAppInitState {
  /** Whether the mini app is initialized */
  isInitialized: boolean;
  /** Whether initialization is in progress */
  isInitializing: boolean;
  /** Initialization error if any */
  error: Error | null;
  /** Retry initialization */
  retry: () => void;
  /** Reset error state */
  resetError: () => void;
}

/**
 * Platform detection result
 */
export type Platform =
  | "ios"
  | "android"
  | "macos"
  | "tdesktop"
  | "web"
  | "unknown";

/**
 * Theme appearance
 */
export type Appearance = "light" | "dark";
