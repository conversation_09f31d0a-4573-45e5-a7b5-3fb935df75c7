import { LgIcon, LgIconName } from '@/components/icons/lg-icon';
import { toast } from '@/components/ui/base.toast';
import { cn } from '@repo/ui/lib/utils';
import Link from 'next/link';
import React from 'react';

interface NavMenuItemProps {
  href: string;
  label: string;
  icon?: LgIconName;
  badge?: string;
  isActive?: boolean;
  isComingSoon?: boolean;
  onMouseEnter?: (event: React.MouseEvent<HTMLDivElement>) => void;
  onMouseLeave?: () => void;
}

export function NavMenuItem({
  href,
  label,
  icon,
  badge,
  isActive,
  isComingSoon,
  onMouseEnter,
  onMouseLeave,
}: NavMenuItemProps) {
  const handleComingSoonClick = (e: React.MouseEvent) => {
    e.preventDefault();
    toast.info('Coming Soon');
  };

  const handleNoActionClick = (e: React.MouseEvent) => {
    e.preventDefault();
    // 아무 동작 없음
  };

  return (
    <div className="relative" onMouseEnter={onMouseEnter} onMouseLeave={onMouseLeave}>
      {href === '#' ? (
        <button
          onClick={handleNoActionClick}
          className={cn(
            'gap-space-8 hover:bg-gray-1 px-space-10 relative flex items-center rounded-xs py-[5px] text-sm font-bold transition-colors',
            isActive && 'bg-gray-1'
          )}
        >
          {icon && <LgIcon className="size-[24px]" name={icon} alt="" />}
          {label}
          {badge && (
            <span className="ml-1 rounded-full bg-red-500 px-1.5 py-0.5 text-xs text-white">
              {badge}
            </span>
          )}
        </button>
      ) : isComingSoon ? (
        <button
          onClick={handleComingSoonClick}
          className={cn(
            'gap-space-8 hover:bg-gray-1 px-space-10 relative flex items-center rounded-xs py-[5px] text-sm font-bold transition-colors',
            isActive && 'bg-gray-1'
          )}
        >
          {icon && <LgIcon className="size-[24px]" name={icon} alt="" />}
          {label}
          {badge && (
            <span className="ml-1 rounded-full bg-red-500 px-1.5 py-0.5 text-xs text-white">
              {badge}
            </span>
          )}
        </button>
      ) : (
        <Link
          href={href}
          className={cn(
            'gap-space-8 hover:bg-gray-1 px-space-10 relative flex items-center rounded-xs py-[5px] text-sm font-bold transition-colors',
            isActive && 'bg-gray-1'
          )}
        >
          {icon && <LgIcon className="size-[24px]" name={icon} alt="" />}
          {label}
          {badge && (
            <span className="ml-1 rounded-full bg-red-500 px-1.5 py-0.5 text-xs text-white">
              {badge}
            </span>
          )}
        </Link>
      )}
    </div>
  );
}
