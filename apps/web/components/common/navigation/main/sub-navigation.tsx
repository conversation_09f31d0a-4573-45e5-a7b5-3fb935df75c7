import { LgIcon } from '@/components/icons/lg-icon';
import { toast } from '@/components/ui/base.toast';
import { cn } from '@repo/ui/lib/utils';
import Link from 'next/link';
import React from 'react';
import { SubMenuItem } from './constants';

interface SubNavigationProps {
  isVisible: boolean;
  items: SubMenuItem[];
  offsetLeft: number;
  pathname: string;
}

export function SubNavigation({ isVisible, items, offsetLeft, pathname }: SubNavigationProps) {
  const handleComingSoonClick = (e: React.MouseEvent) => {
    e.preventDefault();
    toast.info('Coming Soon');
  };

  return (
    <div
      className={`bg-gray-2 absolute top-[var(--nav-height)] right-0 left-0 z-20 overflow-hidden border-b border-gray-200 transition-all duration-300 ease-in-out ${
        isVisible
          ? 'h-[var(--sub-nav-height)] translate-y-0 transform opacity-100'
          : 'h-0 -translate-y-2 transform opacity-0'
      }`}
    >
      <div
        className="flex h-[var(--sub-nav-height)] items-center"
        style={{ paddingLeft: `${offsetLeft}px` }}
      >
        {isVisible &&
          items.map(subItem =>
            subItem.isComingSoon ? (
              <button
                key={subItem.href}
                onClick={e => handleComingSoonClick(e)}
                className="text-dark group py-space-6 px-space-12 mr-4 flex cursor-pointer items-center gap-2 rounded-sm text-sm font-semibold transition-colors duration-200"
              >
                {subItem.icon && (
                  <span className="border-line group-hover:border-sky rounded-xs border-[1px] bg-white px-[2px] py-[1px]">
                    <LgIcon size={20} name={subItem.icon} alt="" />
                  </span>
                )}
                {subItem.label}
              </button>
            ) : (
              <Link
                key={subItem.href}
                href={subItem.href}
                className={cn(
                  'text-dark group py-space-6 px-space-12 mr-4 flex items-center gap-2 rounded-sm text-sm font-semibold transition-colors duration-200'
                )}
              >
                {subItem.icon && (
                  <span
                    className={cn(
                      'border-line group-hover:border-sky rounded-xs border-[1px] bg-white px-[2px] py-[1px] transition-colors',
                      pathname === subItem.href && 'border-sky'
                    )}
                  >
                    <LgIcon size={20} name={subItem.icon} alt="" />
                  </span>
                )}
                {subItem.label}
              </Link>
            )
          )}
      </div>
    </div>
  );
}
