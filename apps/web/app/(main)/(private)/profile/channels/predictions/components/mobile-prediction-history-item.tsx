import CommonAvatar from '@/components/ui/avatar-image';
import type { ChannelHistoryPrediction } from '@/lib/api/channel/channel.schema.server';
import { INNER_LINKS } from '@/lib/constants';
import { formatDate, formatUsdc } from '@/lib/format';
import { Popover, PopoverContent, PopoverTrigger } from '@repo/ui/components/popover';
import BigNumber from 'bignumber.js';
import Link from 'next/link';
import { useState } from 'react';

interface MobilePredictionHistoryItemProps {
  prediction: ChannelHistoryPrediction;
}

export default function MobilePredictionHistoryItem({
  prediction,
}: MobilePredictionHistoryItemProps) {
  const { imageUrl, title, finalizedAt, rewards, outcomes, shortId } = prediction;
  const { prediction: predictionReward, dispute } = rewards;
  const totalRewardsBN = BigNumber(predictionReward).plus(dispute);
  const totalRewards = totalRewardsBN.toString();
  const predictionBN = BigNumber(predictionReward);
  const disputeBN = BigNumber(dispute);
  const [isOpen, setIsOpen] = useState(false);

  const isNegativeTotal = totalRewardsBN.isNegative();
  const isNegativePrediction = predictionBN.isNegative();
  const isNegativeDispute = disputeBN.isNegative();

  return (
    <div className="flex gap-[10px]">
      <CommonAvatar size="md2" imageUrl={imageUrl} />
      <div className="flex flex-1 flex-col gap-[10px]">
        <div className="flex justify-between gap-4">
          <div className="flex min-w-0 flex-1 flex-col">
            <Link href={INNER_LINKS.MAIN.PREDICTIONS.DETAIL(shortId)}>
              <h3 className="text-mid-dark line-clamp-2 max-w-full text-sm font-semibold hover:underline">
                {title}
              </h3>
            </Link>
            {/* <div className="text-size-xs text-icon-dark font-semibold">
              {formatDate(finalizedAt)}
            </div> */}
          </div>
          <div className="flex flex-col items-end">
            <Popover open={isOpen} onOpenChange={setIsOpen}>
              <PopoverTrigger asChild>
                <button
                  className={`text-sky flex items-center gap-1 text-sm font-semibold ${
                    isNegativeTotal ? 'text-no-red' : 'text-mid-dark'
                  }`}
                  onMouseEnter={() => setIsOpen(true)}
                  onMouseLeave={() => setIsOpen(false)}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 18 18"
                    fill="none"
                  >
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M5.27493 2.24988C4.44651 2.24988 3.77493 2.92145 3.77493 3.74988C3.77493 4.57831 4.44651 5.24988 5.27493 5.24988C6.10336 5.24988 6.77493 4.57831 6.77493 3.74988C6.77493 2.92145 6.10336 2.24988 5.27493 2.24988ZM2.27493 3.74988C2.27493 2.09302 3.61808 0.749878 5.27493 0.749878C6.93179 0.749878 8.27493 2.09302 8.27493 3.74988C8.27493 5.40673 6.93179 6.74988 5.27493 6.74988C3.61808 6.74988 2.27493 5.40673 2.27493 3.74988ZM12 5.32495C11.213 5.32495 10.575 5.96294 10.575 6.74995C10.575 7.53696 11.213 8.17495 12 8.17495C12.3373 8.17495 12.6473 8.05775 12.8913 7.86184L13.0424 7.72152C13.2798 7.46698 13.425 7.12542 13.425 6.74995C13.425 5.96294 12.787 5.32495 12 5.32495ZM14.9247 6.70923C14.9029 5.11257 13.6018 3.82495 12 3.82495C10.3846 3.82495 9.075 5.13452 9.075 6.74995C9.075 7.00921 9.10873 7.2606 9.17204 7.49995H7.5C6.91193 7.49995 6.30013 7.69299 5.89194 8.19619L2.25955 11.6988L2.03033 11.4696C1.73744 11.1767 1.26256 11.1767 0.96967 11.4696C0.676777 11.7625 0.676777 12.2374 0.96967 12.5303L1.71844 13.2791L1.72128 13.2819L4.71557 16.2762C4.71863 16.2793 4.72172 16.2824 4.72482 16.2854L5.46967 17.0303C5.76256 17.3232 6.23744 17.3232 6.53033 17.0303C6.82322 16.7374 6.82322 16.2625 6.53033 15.9696L6.34719 15.7865L6.94388 15.2644C6.98314 15.23 7.0187 15.1917 7.05 15.15C7.09255 15.0932 7.20857 15 7.5 15H10.5C11.4988 15 12.4558 14.637 13.141 13.8703L16.5651 10.5951L16.5669 10.5934C16.9998 10.1834 17.2524 9.61837 17.2693 9.02232C17.2862 8.42544 17.0652 7.84631 16.6551 7.41232C16.245 6.97833 15.6792 6.72504 15.0824 6.70817C15.0297 6.70668 14.9771 6.70704 14.9247 6.70923ZM13.8789 8.99175C13.9545 8.92831 14.0269 8.86112 14.0957 8.79046L14.4978 8.41705L14.5026 8.41256C14.6475 8.27567 14.8408 8.20194 15.04 8.20757C15.2392 8.2132 15.428 8.29774 15.5649 8.44258C15.7018 8.58743 15.7755 8.78072 15.7699 8.97994C15.7643 9.17915 15.6797 9.36796 15.5349 9.50485L12.0816 12.808C12.0655 12.8233 12.0502 12.8394 12.0356 12.8561C11.6722 13.2713 11.1398 13.5 10.5 13.5H7.5C6.91821 13.5 6.3132 13.6889 5.90508 14.1802L5.28418 14.7235L3.32039 12.7597L6.9706 9.23983C6.99942 9.21204 7.02598 9.18198 7.05 9.14995C7.09255 9.09322 7.20857 8.99995 7.5 8.99995H9.75C9.94891 8.99995 10.1397 9.07897 10.2803 9.21962C10.421 9.36027 10.5 9.55104 10.5 9.74995C10.5 9.94886 10.421 10.1396 10.2803 10.2803C10.1397 10.4209 9.94891 10.5 9.75 10.5H8.25C7.83579 10.5 7.5 10.8357 7.5 11.25C7.5 11.6642 7.83579 12 8.25 12H9.75C10.3223 12 10.8721 11.7819 11.2884 11.3919C11.3088 11.3764 11.3287 11.3598 11.3478 11.342L13.8789 8.99175Z"
                      fill="#5AC8FA"
                    />
                  </svg>
                  {formatUsdc(totalRewards)}
                </button>
              </PopoverTrigger>
              <PopoverContent
                className="w-max min-w-[230px]"
                onMouseEnter={() => setIsOpen(true)}
                onMouseLeave={() => setIsOpen(false)}
              >
                <div className="space-y-4">
                  <h3 className="text-[12px] font-semibold">Payout Details</h3>
                  <div className="flex flex-col gap-[12px]">
                    <div className="flex items-center justify-between text-[12px]">
                      <span className="text-gray-3 font-medium">Channel Reward</span>
                      <span
                        className={`font-semibold ${isNegativePrediction ? 'text-no-red' : ''}`}
                      >
                        {formatUsdc(predictionReward)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between text-[12px]">
                      <span className="text-gray-3 font-medium">Dispute Payout</span>
                      <span className={`font-semibold ${isNegativeDispute ? 'text-no-red' : ''}`}>
                        {formatUsdc(dispute)}
                      </span>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </div>
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center justify-between gap-8">
            <div className="flex flex-col">
              <span className="text-gray-3 text-xs font-semibold">Initial Outcome</span>
              <span className="text-icon-dark text-sm font-semibold">{outcomes.initial}</span>
            </div>
            <div className="flex flex-col">
              <span className="text-gray-3 text-xs font-semibold">Final Outcome</span>
              <span className="text-icon-dark text-sm font-semibold">{outcomes.final}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
