'use client';

import { AvatarImageUploader } from '@/components/ui/avatar-image-uploader';
import { BaseButton, InfoButton } from '@/components/ui/base.button';
import { InputWithCharacterCount } from '@/components/ui/input-with-character-count';
import ExamplePopup from '@/components/ui/popup/example-popup';
import { ICON_PATH } from '@/lib/constants';
import { Form, FormControl, FormField, FormItem } from '@repo/ui/components/form';
import { cn } from '@repo/ui/lib/utils';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useRef, useState } from 'react';
import { DatetimeSection } from './_components/datetime-section';
import { TermsSubmitSection } from './_components/terms-submit-section';
import { PLACEHOLDER_TEXT } from './types';
import { UrlsDescriptionSection } from './_components/urls-description-section';
import { useCreateMarketForm } from './_hooks/use-create-market-form';

function CreatePredictionContent() {
  const router = useRouter();
  const titleInputRef = useRef<HTMLInputElement | null>(null);
  const [duplicateOutcomes, setDuplicateOutcomes] = useState<number[]>([]);
  const [titleEthicalReviewFailed, setTitleEthicalReviewFailed] = useState(false);
  const [isTermsAccepted, setIsTermsAccepted] = useState(false);
  const [showExamplePopup, setShowExamplePopup] = useState(false);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState('');

  const handleImageFileSelect = (file: File | null) => {
    setImageFile(file);
    if (file) {
      const reader = new FileReader();
      reader.onload = e => {
        setImagePreview((e.target?.result as string) || '');
      };
      reader.readAsDataURL(file);
    } else {
      setImagePreview('');
    }
  };

  const {
    form,
    outcomes,
    onSubmit,
    createMarketTransaction,
    addOutcome,
    removeOutcome,
    updateOutcome,
  } = useCreateMarketForm({
    duplicateOutcomes,
    setDuplicateOutcomes,
    titleEthicalReviewFailed,
    setTitleEthicalReviewFailed,
    imageFile,
  });

  return (
    <div className="page">
      <div className="">
        <button
          type="button"
          onClick={() => router.back()}
          className="mb-[30px] flex items-center gap-2 text-gray-700 transition-colors hover:text-gray-900"
        >
          {/* <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.8611 15L6 10M6 10L10.8611 5M6 10H18M2 2V18"
              stroke="black"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg> */}
          <span className="font-size-lg text-mid-dark font-bold">Create Prediction</span>
        </button>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="gap-space-30 flex w-full flex-col">
          <section
            data-role="section"
            className="gap-space-30 flex items-center max-lg:items-start max-lg:gap-4"
          >
            <AvatarImageUploader
              imageUrl={imagePreview}
              onFileSelect={handleImageFileSelect}
              size={80}
              maxSize={1 * 1024 * 1024} // 1MB
              acceptedTypes={['image/jpeg', 'image/png']}
            />
            <div className="text-sm text-gray-500">
              <div className="mb-1">
                Please upload a representative image for this prediction. If none is uploaded, a
                default image will be automatically applied.
              </div>
              <div>
                Recommended size: at least <b>80 x 80 pixels</b>.
                <br />
                Accepted formats: <b>JPG or PNG</b>.
                <br />
                Maximum file size: <b>1MB</b>.
              </div>
            </div>
          </section>

          <section>
            <header className="mb-[15px] flex items-center justify-between text-sm font-medium max-lg:flex-col max-lg:items-start">
              <div className="max-lg:mb-[10px]">Question</div>
              <div className="gap-space-10 flex items-center"></div>
            </header>
            <div>
              <div className="mb-[10px] flex items-center justify-between gap-[8px]">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem data-role="write-market-title" className="flex-1 space-y-2">
                      <FormControl>
                        <div className="relative">
                          <InputWithCharacterCount
                            ref={titleInputRef}
                            placeholder={PLACEHOLDER_TEXT['market-title']}
                            maxLength={100}
                            value={field.value}
                            onChange={e => {
                              field.onChange(e.target.value);
                            }}
                            className={cn(
                              titleEthicalReviewFailed ? 'border-red-500 focus:border-red-500' : '',
                              form.formState.errors.title
                                ? 'border-red-500 focus:border-red-500'
                                : ''
                            )}
                          />
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <div data-role="market-outcomes" className="gap-space-10 flex flex-col">
                <div className="gap-space-10 flex flex-col">
                  {outcomes.map((outcome, index) => (
                    <div key={index} className="gap-space-10 flex items-center">
                      <div className="text-size-xs font-semibold">{index + 1}</div>
                      <div className="relative flex-1">
                        <InputWithCharacterCount
                          className={cn(
                            duplicateOutcomes.includes(index)
                              ? 'border-red-500 focus:border-red-500'
                              : '',
                            form.formState.errors.outcomes?.[index]
                              ? 'border-red-500 focus:border-red-500'
                              : ''
                          )}
                          placeholder={PLACEHOLDER_TEXT['market-outcomes']}
                          value={outcome}
                          maxLength={50}
                          onValueChange={value => updateOutcome(index, value)}
                          characterCountPosition="bottom-right"
                          characterCountClassName="bottom-3 right-3"
                        />
                      </div>
                      {outcomes.length > 2 && (
                        <button
                          type="button"
                          onClick={() => removeOutcome(index)}
                          className="rounded-sm p-1 hover:bg-gray-100"
                        >
                          <svg
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <rect width="16" height="16" rx="8" fill="#3B424B" />
                            <path
                              d="M11.129 4.8712C11.3788 5.12101 11.3788 5.52649 11.129 5.7763L8.9051 8.00022L11.129 10.2241C11.3788 10.4739 11.3788 10.8794 11.129 11.1292C10.8792 11.379 10.4737 11.379 10.2239 11.1292L8 8.90532L5.77608 11.1292C5.52627 11.379 5.12079 11.379 4.87098 11.1292C4.62104 10.8793 4.62117 10.4739 4.87098 10.2241L7.0949 8.00022L4.87098 5.7763C4.62104 5.52636 4.62118 5.12101 4.87098 4.8712C5.12079 4.6214 5.52614 4.62126 5.77608 4.8712L8 7.09512L10.2239 4.8712C10.4737 4.6214 10.8791 4.62126 11.129 4.8712Z"
                              fill="white"
                            />
                          </svg>
                        </button>
                      )}
                    </div>
                  ))}
                </div>
                {outcomes.length < 10 && (
                  <BaseButton
                    type="button"
                    variant="neutral"
                    width={125}
                    height={30}
                    fontSize={12}
                    className="justify-center self-end font-semibold"
                    onClick={addOutcome}
                  >
                    <div>
                      <Image src={ICON_PATH.BTN_ICON} alt="Add Outcome" width={20} height={20} />
                    </div>
                    Add Outcome
                  </BaseButton>
                )}
              </div>
            </div>
          </section>

          <DatetimeSection control={form.control} />
          <UrlsDescriptionSection
            control={form.control}
            setShowExamplePopup={setShowExamplePopup}
          />
          <TermsSubmitSection
            isTermsAccepted={isTermsAccepted}
            setIsTermsAccepted={setIsTermsAccepted}
          />

          <div className="mt-8 flex space-x-4 max-lg:mt-2">
            <InfoButton
              type="submit"
              className="disabled:bg-icon-gray h-[46px] w-[220px] text-base font-bold disabled:text-white/75 disabled:opacity-100 max-lg:h-[56px] max-lg:w-full"
              disabled={createMarketTransaction.isPending || !isTermsAccepted}
            >
              <img src={ICON_PATH.SYMBOL} alt="Symbol" width={24} height={24} />
              Create Prediction
            </InfoButton>
          </div>
        </form>
      </Form>

      {showExamplePopup && (
        <ExamplePopup isOpen={showExamplePopup} onClose={() => setShowExamplePopup(false)} />
      )}
    </div>
  );
}

export default function CreatePredictionRefactoredPage() {
  return <CreatePredictionContent />;
}
